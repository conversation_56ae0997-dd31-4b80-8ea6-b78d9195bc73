    // Simple canvas initialization that bypasses complex wrapper
    function initializeSimpleCanvas() {
        try {
            console.log('🔄 Checking Fabric.js availability...');
            if (typeof fabric === 'undefined') {
                throw new Error('Fabric.js not loaded');
            }
            console.log('✅ Fabric.js available (version: ' + fabric.version + ')');

            // Hide loading overlay
            const overlay = document.getElementById('canvasOverlay');
            if (overlay) {
                overlay.classList.add('hidden');
            }

            // Create simple canvas directly
            console.log('🔄 Creating canvas...');
            const canvas = new fabric.Canvas('fabricCanvas', {
                width: 800,
                height: 400,
                backgroundColor: '#ffffff',
                selection: true,
                preserveObjectStacking: true,
                enableRetinaScaling: true
            });

            // Add thick black border to canvas only
            const canvasElement = document.getElementById('fabricCanvas');

            if (canvasElement) {
                canvasElement.style.border = '4px solid #000000';
                canvasElement.style.borderRadius = '8px';
                canvasElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                canvasElement.style.display = 'block';
                canvasElement.style.margin = '0 auto';
                console.log('✅ Canvas styled directly without container');
            }

            if (!canvas) {
                throw new Error('Failed to create canvas');
            }

            console.log('✅ Canvas created successfully');

            // Setup responsive behavior - Fixed for desktop/mobile compatibility
            function resizeCanvas() {
                const canvasSection = document.querySelector('.canvas-section');
                if (!canvasSection || !canvas) return;

                // Get available space
                const sectionWidth = canvasSection.clientWidth - 40; // More padding for desktop
                const sectionHeight = canvasSection.clientHeight - 40;

                // Original canvas dimensions
                const originalWidth = 800;
                const originalHeight = 400;

                // Calculate scale to fit container while maintaining aspect ratio
                const scaleX = sectionWidth / originalWidth;
                const scaleY = sectionHeight / originalHeight;
                const scale = Math.min(scaleX, scaleY, 1); // Never scale up beyond 100%

                console.log('📐 Responsive canvas:', {
                    sectionSize: `${sectionWidth}x${sectionHeight}`,
                    scale: scale,
                    finalSize: `${originalWidth * scale}x${originalHeight * scale}`
                });

                // Apply scaling using CSS transform instead of Fabric.js zoom
                // This prevents object distortion issues
                const canvasWrapper = document.getElementById('canvasWrapper');

                if (canvasWrapper && scale < 1) {
                    // For smaller screens, use CSS transform scaling
                    canvas.setDimensions({
                        width: originalWidth,
                        height: originalHeight
                    });

                    canvasWrapper.style.transform = `scale(${scale})`;
                    canvasWrapper.style.transformOrigin = 'center center';
                    canvasWrapper.style.width = `${originalWidth}px`;
                    canvasWrapper.style.height = `${originalHeight}px`;

                    console.log('✅ Applied CSS transform scaling for responsive display');
                } else {
                    // For desktop/larger screens, keep original size
                    canvas.setDimensions({
                        width: originalWidth,
                        height: originalHeight
                    });

                    if (canvasWrapper) {
                        canvasWrapper.style.transform = 'scale(1)';
                        canvasWrapper.style.width = `${originalWidth}px`;
                        canvasWrapper.style.height = `${originalHeight}px`;
                    }

                    console.log('✅ Canvas displayed at original size for desktop');
                }

                canvas.renderAll();
            }

            window.addEventListener('resize', resizeCanvas);
            setTimeout(resizeCanvas, 100);

            // Setup responsive layout switching
            setupResponsiveLayout();

            // Store canvas globally for toolbar access
            window.billboardCanvas = canvas;

            console.log('🎉 Billboard Editor initialized successfully!');

            // Create simple canvas manager wrapper
            const canvasManager = {
                canvas: canvas, // Add direct canvas reference
                getCanvas: () => canvas,
                addText: (text) => {
                    const textObj = new fabric.Text(text, {
                        left: 100,
                        top: 100,
                        fontFamily: 'Arial',
                        fontSize: 20,
                        fill: '#000000'
                    });
                    canvas.add(textObj);
                    canvas.setActiveObject(textObj);
                    return textObj;
                },
                setBackgroundColor: (color) => {
                    canvas.setBackgroundColor(color, canvas.renderAll.bind(canvas));
                },
                setBackgroundImage: (url) => {
                    return new Promise((resolve, reject) => {
                        fabric.Image.fromURL(url, (img) => {
                            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                                scaleX: canvas.width / img.width,
                                scaleY: canvas.height / img.height
                            });
                            resolve();
                        }, { crossOrigin: 'anonymous' });
                    });
                },
                clearBackground: () => {
                    canvas.setBackgroundImage(null, canvas.renderAll.bind(canvas));
                    canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
                },
                on: (event, handler) => {
                    canvas.on(event, handler);
                }
            };

            // Initialize mobile toolbar manager
            if (typeof MobileToolbarManager !== 'undefined') {
                window.toolbarManager = new MobileToolbarManager(canvasManager);
                console.log('✅ MobileToolbarManager initialized');
            } else {
                // Fallback to basic toolbar
                initializeBasicToolbar(canvas);
                console.log('⚠️ Using basic toolbar fallback');
            }

            // Initialize checkout system
            initializeCheckoutSystem();

        } catch (error) {
            console.error('❌ Initialization failed:', error);
            const loadingElement = document.getElementById('canvasLoading');
            if (loadingElement) {
                loadingElement.innerHTML = `
                    <div style="text-align: center; color: #dc2626;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <h3>Initialization Failed</h3>
                        <p>${error.message}</p>
                        <button onclick="window.location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #2563eb; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </div>
                `;
            }
        }
    }
    