<?php
// Include the shared header component
$pageTitle = "Billboard Designer - Borges Media";
$headerTitle = "Billboard Designer";
$headerSubtitle = "Create your perfect billboard design";
$headerIcon = "fas fa-paint-brush";
$additionalCSS = [
    "assets/css/mobile-first-styles.css",
    "assets/css/fabric-canvas-styles.css",
    "assets/css/mobile-toolbar-styles.css",
    "assets/css/responsive-modal-styles.css",
    "assets/css/color-background-picker.css",
    "assets/css/sticker-interface.css",
    "assets/css/sticker-controls.css",
    "../shared/checkout-modal.css"
];
include '../shared/header.php';
?>

<div class="mobile-billboard-editor" id="mobileEditor">
    <!-- Desktop Left Sidebar - Design Tools -->
    <div class="desktop-left-sidebar" id="desktopLeftSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">Design Tools</h3>
        </div>
        <div class="sidebar-content" id="leftSidebarContent">
            <!-- Design tools will be moved here via JavaScript for desktop -->
        </div>
    </div>

    <!-- Canvas with Proper Wrapper for Responsive Scaling -->
    <div class="canvas-section">
        <div class="canvas-wrapper" id="canvasWrapper">
            <canvas id="fabricCanvas" class="fabric-canvas"></canvas>
        </div>
        <div class="canvas-overlay" id="canvasOverlay">
            <div class="canvas-loading" id="canvasLoading">
                <div class="loading-spinner"></div>
                <span>Loading Canvas...</span>
            </div>
        </div>
    </div>

    <!-- Desktop Right Sidebar - Export & Checkout -->
    <div class="desktop-right-sidebar" id="desktopRightSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">Export & Checkout</h3>
        </div>
        <div class="sidebar-content" id="rightSidebarContent">
            <!-- Export tools will be moved here via JavaScript for desktop -->
        </div>
    </div>

    <!-- Mobile-First Toolbar (Hidden on Desktop) -->
    <div class="mobile-toolbar" id="mobileToolbar">
        <!-- Toolbar Header -->
        <div class="toolbar-header">
            <h3 class="toolbar-title">Design Tools</h3>
            <button class="toolbar-toggle" id="toolbarToggle" aria-label="Toggle toolbar">
                <i class="fas fa-chevron-up"></i>
            </button>
        </div>

        <!-- Toolbar Content -->
        <div class="toolbar-content" id="toolbarContent">
            <!-- Background Tools Section -->
            <div class="tool-section" id="backgroundSection">
                <div class="section-header">
                    <h4 class="section-title">Background</h4>
                    <button class="section-toggle" data-target="backgroundTools" aria-label="Toggle background tools">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="section-content" id="backgroundTools">
                    <div class="tool-grid">
                        <button class="tool-btn primary" id="chooseBackground" aria-label="Choose background">
                            <i class="fas fa-image"></i>
                            <span>Choose Background</span>
                        </button>
                        <button class="tool-btn secondary" id="clearBackground" aria-label="Clear background">
                            <i class="fas fa-times"></i>
                            <span>Clear Background</span>
                        </button>
                        <button class="tool-btn secondary" id="addImage" aria-label="Add image">
                            <i class="fas fa-plus"></i>
                            <span>Add Image</span>
                        </button>
                        <button class="tool-btn secondary" id="addSticker" aria-label="Add sticker">
                            <i class="fas fa-star"></i>
                            <span>Add Sticker</span>
                        </button>
                        <button class="tool-btn danger" id="clearAll" aria-label="Clear all">
                            <i class="fas fa-trash"></i>
                            <span>Clear All</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Text Tools Section -->
            <div class="tool-section" id="textSection">
                <div class="section-header">
                    <h4 class="section-title">Text</h4>
                    <button class="section-toggle" data-target="textTools" aria-label="Toggle text tools">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="section-content" id="textTools">
                    <div class="tool-grid">
                        <button class="tool-btn primary" id="addText" aria-label="Add text">
                            <i class="fas fa-font"></i>
                            <span>Add Text</span>
                        </button>
                    </div>
                    
                    <!-- Text Properties Panel -->
                    <div class="text-properties" id="textProperties" style="display: none;">
                        <!-- Font Family -->
                        <div class="property-group">
                            <label for="fontFamily" class="property-label">Font Family</label>
                            <select id="fontFamily" class="property-select">
                                <option value="Inter">Inter</option>
                                <option value="Roboto">Roboto</option>
                                <option value="Montserrat">Montserrat</option>
                                <option value="Open Sans">Open Sans</option>
                                <option value="Lato">Lato</option>
                                <option value="Poppins">Poppins</option>
                            </select>
                        </div>

                        <!-- Font Size -->
                        <div class="property-group">
                            <label for="fontSize" class="property-label">Font Size</label>
                            <div class="input-with-controls">
                                <input type="number" id="fontSize" class="property-input" min="8" max="200" value="24">
                                <div class="input-controls">
                                    <button class="input-btn" id="fontSizeDown" aria-label="Decrease font size">-</button>
                                    <button class="input-btn" id="fontSizeUp" aria-label="Increase font size">+</button>
                                </div>
                            </div>
                        </div>

                        <!-- Text Style Controls -->
                        <div class="property-group">
                            <label class="property-label">Text Style</label>
                            <div class="toggle-group">
                                <button class="toggle-btn" id="boldToggle" aria-label="Bold">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button class="toggle-btn" id="italicToggle" aria-label="Italic">
                                    <i class="fas fa-italic"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Text Alignment -->
                        <div class="property-group">
                            <label class="property-label">Alignment</label>
                            <div class="toggle-group">
                                <button class="toggle-btn" id="alignLeft" data-align="left" aria-label="Align left">
                                    <i class="fas fa-align-left"></i>
                                </button>
                                <button class="toggle-btn" id="alignCenter" data-align="center" aria-label="Align center">
                                    <i class="fas fa-align-center"></i>
                                </button>
                                <button class="toggle-btn" id="alignRight" data-align="right" aria-label="Align right">
                                    <i class="fas fa-align-right"></i>
                                </button>
                                <button class="toggle-btn" id="alignJustify" data-align="justify" aria-label="Justify">
                                    <i class="fas fa-align-justify"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Font Color -->
                        <div class="property-group">
                            <label for="fontColor" class="property-label">Font Color</label>
                            <div class="color-picker-wrapper">
                                <input type="color" id="fontColor" class="color-picker" value="#000000">
                                <div class="color-preview" id="fontColorPreview"></div>
                            </div>
                        </div>

                        <!-- Text Shadow Section -->
                        <div class="property-group">
                            <div class="property-header">
                                <label class="property-label">Text Shadow</label>
                                <button class="toggle-btn shadow-toggle" id="shadowToggle" aria-label="Toggle shadow">
                                    <span>S</span>
                                </button>
                            </div>
                            
                            <div class="shadow-controls" id="shadowControls" style="display: none;">
                                <!-- Shadow Color -->
                                <div class="sub-property">
                                    <label for="shadowColor" class="sub-label">Shadow Color</label>
                                    <div class="color-picker-wrapper">
                                        <input type="color" id="shadowColor" class="color-picker" value="#000000">
                                        <div class="color-preview" id="shadowColorPreview"></div>
                                    </div>
                                </div>

                                <!-- Shadow Type Selector -->
                                <div class="sub-property">
                                    <label for="shadowType" class="sub-label">Shadow Type</label>
                                    <select id="shadowType" class="property-select">
                                        <option value="glow">Glow (No Offset)</option>
                                        <option value="drop">Drop Shadow</option>
                                    </select>
                                </div>

                                <!-- Shadow Blur -->
                                <div class="sub-property">
                                    <label for="shadowBlur" class="sub-label">Blur</label>
                                    <input type="range" id="shadowBlur" class="range-slider" min="1" max="30" value="5">
                                    <span class="range-value" id="shadowBlurValue">5</span>
                                </div>

                                <!-- Shadow Offset Controls (only for drop shadow) -->
                                <div id="offsetControls" style="display: none;">
                                    <!-- Shadow Offset X -->
                                    <div class="sub-property">
                                        <label for="shadowOffsetX" class="sub-label">Offset X</label>
                                        <input type="range" id="shadowOffsetX" class="range-slider" min="-20" max="20" value="2">
                                        <span class="range-value" id="shadowOffsetXValue">2</span>
                                    </div>

                                    <!-- Shadow Offset Y -->
                                    <div class="sub-property">
                                        <label for="shadowOffsetY" class="sub-label">Offset Y</label>
                                        <input type="range" id="shadowOffsetY" class="range-slider" min="-20" max="20" value="2">
                                        <span class="range-value" id="shadowOffsetYValue">2</span>
                                    </div>
                                </div>

                                <!-- Shadow Opacity -->
                                <div class="sub-property">
                                    <label for="shadowOpacity" class="sub-label">Opacity</label>
                                    <input type="range" id="shadowOpacity" class="range-slider" min="0" max="100" value="100">
                                    <span class="range-value" id="shadowOpacityValue">100%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sticker Properties Panel -->
                    <div class="sticker-properties" id="stickerProperties" style="display: none;">
                        <h4>
                            <i class="fas fa-star"></i>
                            Sticker Properties
                        </h4>
                        <div id="stickerColorPickerContainer">
                            <!-- Color picker will be inserted here by StickerControlsManager -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Tools Section -->
            <div class="tool-section" id="exportSection">
                <div class="section-header">
                    <h4 class="section-title">Export & Checkout</h4>
                    <button class="section-toggle" data-target="exportTools" aria-label="Toggle export tools">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="section-content" id="exportTools">
                    <!-- Export Quality -->
                    <div class="property-group">
                        <label for="exportQuality" class="property-label">Export Quality</label>
                        <select id="exportQuality" class="property-select">
                            <option value="web">Web (1x - ~500KB)</option>
                            <option value="standard" selected>Standard (2x - ~2MB)</option>
                            <option value="high">High (3x - ~5MB)</option>
                            <option value="ultra">Ultra HD (4x - ~8MB)</option>
                            <option value="super">Super HD (6x - ~13MB)</option>
                        </select>
                    </div>

                    <!-- Export Format -->
                    <div class="property-group">
                        <label for="exportFormat" class="property-label">Export Format</label>
                        <select id="exportFormat" class="property-select">
                            <option value="png" selected>PNG</option>
                            <option value="jpeg">JPEG</option>
                        </select>
                    </div>

                    <!-- Export Actions -->
                    <div class="tool-grid">
                        <button class="tool-btn primary" id="downloadImage" aria-label="Download image">
                            <i class="fas fa-download"></i>
                            <span>Download Image</span>
                        </button>
                        <button class="tool-btn success" id="checkoutBtn" aria-label="Proceed to checkout">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Checkout</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Background Template Modal -->
<div class="modal-overlay" id="backgroundModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title" id="modalTitle">Choose Background</h3>
            <button class="modal-close" id="modalClose" aria-label="Close modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- Background Type Tabs -->
        <div class="modal-tabs" id="modalTabs">
            <button class="modal-tab active" data-tab="images">
                <i class="fas fa-images"></i>
                <span>Images</span>
            </button>
            <button class="modal-tab" data-tab="colors">
                <i class="fas fa-palette"></i>
                <span>Colors</span>
            </button>
        </div>

        <div class="modal-content" id="modalContent">
            <!-- Images Tab Content -->
            <div class="modal-tab-content active" id="imagesTabContent">
                <!-- Step 1: Category Selection -->
                <div class="modal-step" id="categoryStep">
                    <div class="category-grid" id="categoryGrid">
                        <!-- Categories will be populated dynamically -->
                    </div>
                </div>

                <!-- Step 2: Template Selection -->
                <div class="modal-step" id="templateStep" style="display: none;">
                    <div class="template-grid" id="templateGrid">
                        <!-- Templates will be populated dynamically -->
                    </div>
                </div>
            </div>

            <!-- Colors Tab Content -->
            <div class="modal-tab-content" id="colorsTabContent" style="display: none;">
                <div id="colorBackgroundContainer">
                    <!-- Color picker will be inserted here -->
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button class="modal-btn secondary" id="modalBack" style="display: none;">
                <i class="fas fa-arrow-left"></i>
                <span>Back</span>
            </button>
            <button class="modal-btn secondary" id="modalCancel">
                <span>Cancel</span>
            </button>
            <button class="modal-btn primary" id="modalApply" disabled>
                <span>Apply Background</span>
            </button>
        </div>
    </div>
</div>

<!-- Hidden file input for image upload -->
<input type="file" id="imageUpload" accept="image/*" style="display: none;">

<!-- Sticker Selection Modal -->
<div class="modal-overlay" id="stickerModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-star"></i>
                <span>Add Stickers</span>
            </h3>
            <button class="modal-close" id="stickerModalClose" aria-label="Close sticker modal">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-content" id="stickerModalContent">
            <div id="stickerContainer">
                <!-- Sticker interface will be populated here -->
            </div>
        </div>

        <div class="modal-footer">
            <button class="modal-btn secondary" id="stickerModalCancel">
                <span>Close</span>
            </button>
        </div>
    </div>
</div>

<!-- Checkout modal will be included at the end -->

<!-- Fabric.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>

<!-- Background Template Data -->
<script src="background-template-data.js"></script>

<!-- Sticker Data -->
<script src="sticker-data.js"></script>

<!-- Checkout System -->
<script src="../shared/checkout-system.js"></script>

<!-- Color Background Manager -->
<script src="assets/js/ColorBackgroundManager.js"></script>

<!-- Mobile Background Manager -->
<script src="assets/js/mobile-background-manager.js"></script>

<!-- Sticker Manager -->
<script src="assets/js/StickerManager.js"></script>

<!-- Sticker Controls Manager -->
<script src="assets/js/StickerControlsManager.js"></script>

<!-- Mobile Toolbar Manager -->
<script src="assets/js/mobile-toolbar-manager.js?v=<?php echo time(); ?>"></script>

<!-- Separate JavaScript Modules -->
<script src="assets/js/initializeSimpleCanvas.js"></script>
<script src="assets/js/initializeBasicToolbar.js"></script>
<script src="assets/js/setupResponsiveLayout.js"></script>
<script src="assets/js/setupStickySidebarEnhancements.js"></script>
<script src="assets/js/preventHeaderOverlap.js"></script>
<script src="assets/js/initializeCheckoutSystem.js"></script>

<!-- Main Initialization Script -->
<script>
    console.log('🔄 Starting Billboard Editor initialization...');
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initializeSimpleCanvas, 500);
        });
    } else {
        setTimeout(initializeSimpleCanvas, 500);
    }
</script>

<!-- Background Template Data -->
<script src="background-template-data.js"></script>

<!-- Include Checkout Modal -->
<?php include '../shared/checkout-modal.php'; ?>

<!-- Checkout Modal System -->
<script src="../shared/checkout-modal.js"></script>
<script src="../shared/order-data-manager.js"></script>

</body>
</html>
